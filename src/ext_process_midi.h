#pragma once

#include <string>
#include <vector>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>
#include <queue>

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h> // For pipe, fork, exec
#include <sys/wait.h> // For waitpid
#include <poll.h> // For poll
#endif

// Forward declaration - MIDIMessage is defined in alsa_midi.h
struct MIDIMessage;

// Callback function type for external process MIDI input
using ExtProcessMIDIInputCallback = std::function<void(const MIDIMessage& message)>;

class ExtProcessMIDIInput {
public:
    ExtProcessMIDIInput();
    ~ExtProcessMIDIInput();

    // Initialize the external process MIDI system (e.g., create pipes)
    bool Initialize();
    
    // Cleanup and release resources
    void Cleanup();
    
    // Start the external MIDI process
    bool StartInput(const std::string& executable_path, const std::vector<std::string>& args);
    
    // Stop the external MIDI process
    void StopInput();
    
    // Check if the external process is currently active
    bool IsInputActive() const;
    
    // Set callback function for MIDI messages
    void SetMIDICallback(ExtProcessMIDIInputCallback callback);

    // Get accumulated stderr log
    std::string GetStderrLog() const;
    
    // Clear stderr log
    void ClearStderrLog();

private:
    std::atomic<bool> initialized_;
    std::atomic<bool> input_active_;
    std::atomic<bool> should_stop_;
    ExtProcessMIDIInputCallback midi_callback_;

    std::unique_ptr<std::thread> input_thread_;
    std::unique_ptr<std::thread> stderr_thread_;

    mutable std::mutex stderr_mutex_;
    std::string stderr_log_;

    // Platform-specific process handles/PIDs
#ifdef _WIN32
    HANDLE child_stdout_read_;
    HANDLE child_stderr_read_;
    PROCESS_INFORMATION pi_proc_info_;
    SECURITY_ATTRIBUTES sa_pipe_attr_;
#else
    pid_t child_pid_;
    int stdout_pipe_[2]; // 0: read, 1: write
    int stderr_pipe_[2]; // 0: read, 1: write
#endif

    // Thread functions
    void InputThreadFunction();
    void StderrThreadFunction();

    // Helper to convert string to MIDIMessage
    MIDIMessage ParseMIDILine(const std::string& line);

    // Cleanup when process terminates unexpectedly
    void CleanupTerminatedProcess();
};
