#include "opengl_renderer.h"
#include <iostream>
#include <cmath>
#include <vector>

OpenGLRenderer::OpenGLRenderer() 
    : window_width_(800), window_height_(600) {
}

OpenGLRenderer::~OpenGLRenderer() {
}

void OpenGLRenderer::Initialize(int window_width, int window_height) {
    window_width_ = window_width;
    window_height_ = window_height;
    
    // Enable blending for transparency
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
    
    SetupProjection();
}

void OpenGLRenderer::SetViewport(int width, int height) {
    window_width_ = width;
    window_height_ = height;
    glViewport(0, 0, width, height);
    SetupProjection();
}

void OpenGLRenderer::Clear(const Color& clear_color) {
    glClearColor(clear_color.r, clear_color.g, clear_color.b, clear_color.a);
    glClear(GL_COLOR_BUFFER_BIT);
}

void OpenGLRenderer::DrawRect(const Vec2& position, const Vec2& size, const Color& color) {
    glColor4f(color.r, color.g, color.b, color.a);
    DrawQuad(position, size);
}

void OpenGLRenderer::DrawRectGradient(const Vec2& position, const Vec2& size,
                                     const Color& top_color, const Color& bottom_color) {
    glBegin(GL_QUADS);

    // Top-left vertex (top color)
    glColor4f(top_color.r, top_color.g, top_color.b, top_color.a);
    glVertex2f(position.x, position.y);

    // Top-right vertex (top color)
    glColor4f(top_color.r, top_color.g, top_color.b, top_color.a);
    glVertex2f(position.x + size.x, position.y);

    // Bottom-right vertex (bottom color)
    glColor4f(bottom_color.r, bottom_color.g, bottom_color.b, bottom_color.a);
    glVertex2f(position.x + size.x, position.y + size.y);

    // Bottom-left vertex (bottom color)
    glColor4f(bottom_color.r, bottom_color.g, bottom_color.b, bottom_color.a);
    glVertex2f(position.x, position.y + size.y);

    glEnd();
}

void OpenGLRenderer::DrawRectGradientRounded(const Vec2& position, const Vec2& size,
                                            const Color& top_color, const Color& bottom_color,
                                            float corner_radius) {
    const int segments = 8; // Number of segments for quarter circle
    const float pi = 3.14159265359f;

    // Helper function to interpolate colors based on Y position
    auto interpolateColor = [&](float y) -> Color {
        float t = (y - position.y) / size.y; // Normalize Y position (0 = top, 1 = bottom)
        t = std::max(0.0f, std::min(1.0f, t)); // Clamp to [0, 1]

        return Color(
            top_color.r + t * (bottom_color.r - top_color.r),
            top_color.g + t * (bottom_color.g - top_color.g),
            top_color.b + t * (bottom_color.b - top_color.b),
            top_color.a + t * (bottom_color.a - top_color.a)
        );
    };

    glBegin(GL_TRIANGLE_FAN);

    // Center point for fan triangulation
    float center_x = position.x + size.x * 0.5f;
    float center_y = position.y + size.y * 0.5f;
    Color center_color = interpolateColor(center_y);
    glColor4f(center_color.r, center_color.g, center_color.b, center_color.a);
    glVertex2f(center_x, center_y);

    // Draw rounded rectangle as triangle fan
    std::vector<Vec2> vertices;

    // Top-left corner
    for (int i = 0; i <= segments; i++) {
        float angle = pi + (pi * 0.5f * i) / segments;
        float x = position.x + corner_radius + corner_radius * cos(angle);
        float y = position.y + corner_radius + corner_radius * sin(angle);
        vertices.push_back(Vec2(x, y));
    }

    // Top-right corner
    for (int i = 0; i <= segments; i++) {
        float angle = 1.5f * pi + (pi * 0.5f * i) / segments;
        float x = position.x + size.x - corner_radius + corner_radius * cos(angle);
        float y = position.y + corner_radius + corner_radius * sin(angle);
        vertices.push_back(Vec2(x, y));
    }

    // Bottom-right corner
    for (int i = 0; i <= segments; i++) {
        float angle = 0.0f + (pi * 0.5f * i) / segments;
        float x = position.x + size.x - corner_radius + corner_radius * cos(angle);
        float y = position.y + size.y - corner_radius + corner_radius * sin(angle);
        vertices.push_back(Vec2(x, y));
    }

    // Bottom-left corner
    for (int i = 0; i <= segments; i++) {
        float angle = 0.5f * pi + (pi * 0.5f * i) / segments;
        float x = position.x + corner_radius + corner_radius * cos(angle);
        float y = position.y + size.y - corner_radius + corner_radius * sin(angle);
        vertices.push_back(Vec2(x, y));
    }

    // Draw all vertices with interpolated colors
    for (const auto& vertex : vertices) {
        Color vertex_color = interpolateColor(vertex.y);
        glColor4f(vertex_color.r, vertex_color.g, vertex_color.b, vertex_color.a);
        glVertex2f(vertex.x, vertex.y);
    }

    // Close the fan by repeating the first vertex
    if (!vertices.empty()) {
        Color first_color = interpolateColor(vertices[0].y);
        glColor4f(first_color.r, first_color.g, first_color.b, first_color.a);
        glVertex2f(vertices[0].x, vertices[0].y);
    }

    glEnd();
}

void OpenGLRenderer::DrawRectWithBorder(const Vec2& position, const Vec2& size,
                                       const Color& fill_color, const Color& border_color,
                                       float border_width) {
    // Draw filled rectangle only if not transparent
    if (fill_color.a > 0.0f) {
        DrawRect(position, size, fill_color);
    }

    // Draw border with smooth lines
    glColor4f(border_color.r, border_color.g, border_color.b, border_color.a);
    glLineWidth(border_width);

    // Enable line smoothing for rounded appearance
    glEnable(GL_LINE_SMOOTH);
    glHint(GL_LINE_SMOOTH_HINT, GL_NICEST);

    glBegin(GL_LINE_LOOP);
    glVertex2f(position.x, position.y);
    glVertex2f(position.x + size.x, position.y);
    glVertex2f(position.x + size.x, position.y + size.y);
    glVertex2f(position.x, position.y + size.y);
    glEnd();

    glDisable(GL_LINE_SMOOTH);
}

void OpenGLRenderer::DrawRectWithRoundedBorder(const Vec2& position, const Vec2& size,
                                              const Color& fill_color, const Color& border_color,
                                              float border_width, float corner_radius) {
    // Draw filled rectangle only if not transparent
    if (fill_color.a > 0.0f) {
        DrawRect(position, size, fill_color);
    }

    // Draw rounded border
    glColor4f(border_color.r, border_color.g, border_color.b, border_color.a);
    glLineWidth(border_width);

    // Enable line smoothing for better appearance
    glEnable(GL_LINE_SMOOTH);
    glHint(GL_LINE_SMOOTH_HINT, GL_NICEST);

    const int segments = 8; // Number of segments for quarter circle
    const float pi = 3.14159265359f;

    glBegin(GL_LINE_STRIP);

    // Top-left corner
    for (int i = 0; i <= segments; i++) {
        float angle = pi + (pi * 0.5f * i) / segments;
        float x = position.x + corner_radius + corner_radius * cos(angle);
        float y = position.y + corner_radius + corner_radius * sin(angle);
        glVertex2f(x, y);
    }

    // Top-right corner
    for (int i = 0; i <= segments; i++) {
        float angle = 1.5f * pi + (pi * 0.5f * i) / segments;
        float x = position.x + size.x - corner_radius + corner_radius * cos(angle);
        float y = position.y + corner_radius + corner_radius * sin(angle);
        glVertex2f(x, y);
    }

    // Bottom-right corner
    for (int i = 0; i <= segments; i++) {
        float angle = 0.0f + (pi * 0.5f * i) / segments;
        float x = position.x + size.x - corner_radius + corner_radius * cos(angle);
        float y = position.y + size.y - corner_radius + corner_radius * sin(angle);
        glVertex2f(x, y);
    }

    // Bottom-left corner
    for (int i = 0; i <= segments; i++) {
        float angle = 0.5f * pi + (pi * 0.5f * i) / segments;
        float x = position.x + corner_radius + corner_radius * cos(angle);
        float y = position.y + size.y - corner_radius + corner_radius * sin(angle);
        glVertex2f(x, y);
    }

    // Close the loop
    float angle = pi;
    float x = position.x + corner_radius + corner_radius * cos(angle);
    float y = position.y + corner_radius + corner_radius * sin(angle);
    glVertex2f(x, y);

    glEnd();
    glDisable(GL_LINE_SMOOTH);
}

void OpenGLRenderer::BeginBatch() {
    batch_rects_.clear();
}

void OpenGLRenderer::AddRect(const Rect& rect) {
    batch_rects_.push_back(rect);
}

void OpenGLRenderer::EndBatch() {
    for (const auto& rect : batch_rects_) {
        DrawRectWithBorder(rect.position, rect.size, rect.color, 
                          rect.border_color, rect.border_width);
    }
    batch_rects_.clear();
}

Vec2 OpenGLRenderer::ScreenToGL(const Vec2& screen_pos) const {
    // Convert from screen coordinates (0,0 top-left) to OpenGL coordinates (-1,-1 bottom-left to 1,1 top-right)
    float gl_x = (screen_pos.x / window_width_) * 2.0f - 1.0f;
    float gl_y = 1.0f - (screen_pos.y / window_height_) * 2.0f;
    return Vec2(gl_x, gl_y);
}

Vec2 OpenGLRenderer::GLToScreen(const Vec2& gl_pos) const {
    // Convert from OpenGL coordinates to screen coordinates
    float screen_x = (gl_pos.x + 1.0f) * 0.5f * window_width_;
    float screen_y = (1.0f - gl_pos.y) * 0.5f * window_height_;
    return Vec2(screen_x, screen_y);
}

void OpenGLRenderer::DrawQuad(const Vec2& position, const Vec2& size) {
    glBegin(GL_QUADS);
    glVertex2f(position.x, position.y);
    glVertex2f(position.x + size.x, position.y);
    glVertex2f(position.x + size.x, position.y + size.y);
    glVertex2f(position.x, position.y + size.y);
    glEnd();
}

void OpenGLRenderer::SetupProjection() {
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    
    // Use screen coordinates (0,0 top-left to width,height bottom-right)
    glOrtho(0.0, window_width_, window_height_, 0.0, -1.0, 1.0);
    
    glMatrixMode(GL_MODELVIEW);
    glLoadIdentity();
}
