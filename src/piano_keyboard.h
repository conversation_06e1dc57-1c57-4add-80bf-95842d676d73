#pragma once

#include <vector>
#include <chrono>
#include "opengl_renderer.h"

// Forward declaration
class AudioEngine;

struct KeyBlip {
    std::chrono::steady_clock::time_point time;  // When the blip was created
    Color color;                                 // Color of the blip
    float y_offset;                             // Vertical offset from key position
};

struct PianoKey {
    int note;           // MIDI note number (0-127)
    bool is_black;      // true if black key, false if white key
    bool is_pressed;    // true if key is currently pressed (mouse input)
    bool is_midi_pressed; // true if key is pressed via MIDI input
    int midi_channel;   // MIDI channel for currently pressed note (-1 if not pressed via MIDI)
    Vec2 position;      // Position on screen
    Vec2 size;          // Size of the key
    Color color;        // Current color of the key
    std::vector<KeyBlip> blips;  // Visual effect blips for this key
    std::chrono::steady_clock::time_point time_played;  // Last time this key was played

    // Animation properties
    bool was_pressed;   // Previous frame pressed state for detecting press/release
    bool was_midi_pressed; // Previous frame MIDI pressed state
    std::chrono::steady_clock::time_point press_time;    // When key was pressed
    std::chrono::steady_clock::time_point release_time;  // When key was released
    float animation_progress; // 0.0 to 1.0, animation progress
    bool is_animating;  // true if currently animating
};

class PianoKeyboard {
public:
    PianoKeyboard();
    ~PianoKeyboard();

    // Initialize the keyboard with 128 keys (0-127)
    void Initialize();

    // Initialize with audio engine
    void Initialize(AudioEngine* audio_engine);
    
    // Update keyboard state
    void Update();
    
    // Render the keyboard using OpenGL
    void Render(OpenGLRenderer& renderer);

    // Handle mouse input
    void HandleInput(double mouse_x, double mouse_y, bool mouse_is_down);

    // Set key pressed with velocity based on mouse position
    void SetKeyPressedWithVelocity(int note, bool pressed, double mouse_y = -1.0);
    
    // Get/Set key state
    bool IsKeyPressed(int note) const;
    void SetKeyPressed(int note, bool pressed);

    // MIDI input key state (separate from mouse input)
    bool IsMIDIKeyPressed(int note) const;
    void SetMIDIKeyPressed(int note, bool pressed);
    void SetMIDIKeyPressed(int note, bool pressed, int channel);
    
    // Configuration
    void SetKeyboardPosition(const Vec2& position);
    void SetKeyboardSize(const Vec2& size);
    void SetWhiteKeySize(const Vec2& size);
    void SetBlackKeySize(const Vec2& size);

    // Auto layout based on window size
    void UpdateLayout(int window_width, int window_height);
    void SetAutoLayout(bool enabled);
    void SetKeyboardMargin(float margin);
    
    // Get keyboard info
    int GetPressedKeyCount() const;
    std::vector<int> GetPressedKeys() const;

    // Audio settings
    void SetAudioEngine(AudioEngine* audio_engine);
    void SetAudioEnabled(bool enabled);
    bool IsAudioEnabled() const;

    // Multioctave settings
    void SetMultioctaveEnabled(bool enabled);
    bool IsMultioctaveEnabled() const;
    void SetMultioctaveCount(int count);
    int GetMultioctaveCount() const;
    void SetMultioctaveDistance(int distance);
    int GetMultioctaveDistance() const;

    // Visual effects
    void AddKeyBlip(int note, const Color& color);
    void UpdateBlips();
    void UpdateKeyAnimations();

private:
    std::vector<PianoKey> keys_;
    Vec2 keyboard_position_;
    Vec2 keyboard_size_;
    Vec2 white_key_size_;
    Vec2 black_key_size_;

    // Auto layout settings
    bool auto_layout_enabled_;
    float keyboard_margin_;
    int current_window_width_;
    int current_window_height_;

    // Colors
    Color white_key_color_;
    Color white_key_pressed_color_;
    Color black_key_color_;
    Color black_key_pressed_color_;
    Color white_key_midi_pressed_color_;
    Color black_key_midi_pressed_color_;
    Color key_border_color_;

    // Audio
    AudioEngine* audio_engine_;
    bool audio_enabled_;
    int last_hovered_key_;

    // Multioctave settings
    bool multioctave_enabled_;
    int multioctave_count_;
    int multioctave_distance_;

    // Mouse position for velocity calculation
    double current_mouse_y_;
    int last_calculated_velocity_;

    // Blip effect settings
    float white_blip_width_;
    float white_blip_height_;
    float white_blip_x_offset_;
    float white_blip_y_offset_;
    float black_blip_width_;
    float black_blip_height_;
    float black_blip_x_offset_;
    float black_blip_y_offset_;
    float blip_fade_duration_ms_;
    float blip_spacing_factor_;

    // Key animation settings
    float key_press_animation_duration_ms_;  // Duration of press animation
    float key_release_animation_duration_ms_; // Duration of release animation
    float key_press_scale_factor_;           // How much to scale key when pressed (0.9 = 90% size)
    float key_press_y_offset_;               // How much to move key down when pressed
    
    // Helper functions
    bool IsBlackKey(int note) const;
    void CalculateKeyPositions();
    int GetWhiteKeyIndex(int note) const;
    void RenderWhiteKeys(OpenGLRenderer& renderer);
    void RenderBlackKeys(OpenGLRenderer& renderer);
    void RenderWhiteKeyBlips(OpenGLRenderer& renderer);
    void RenderBlackKeyBlips(OpenGLRenderer& renderer);
    int GetKeyAtPosition(const Vec2& pos) const;
    Color GetChannelColor(int channel) const;

    // Layout calculation helpers
    void CalculateAutoLayout(int window_width, int window_height);
    int GetTotalWhiteKeys() const;

    // Velocity calculation based on mouse position
    int CalculateVelocityFromMouseY(double mouse_y) const;
};
