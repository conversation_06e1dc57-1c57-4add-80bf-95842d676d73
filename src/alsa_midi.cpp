#include "alsa_midi.h"
#include <iostream>
#include <cstring>
#include <chrono>

#ifdef __linux__
#include <poll.h>
#include <errno.h>
#include <pthread.h>
#include <sched.h>
#include <unistd.h>

// Helper function to set real-time priority for ALSA MIDI thread
static void SetALSARealtimePriority() {
    struct sched_param param;
    param.sched_priority = 85; // Slightly higher priority than worker threads

    if (pthread_setschedparam(pthread_self(), SCHED_FIFO, &param) != 0) {
        // If real-time scheduling fails, try high normal priority
        param.sched_priority = 0;
        pthread_setschedparam(pthread_self(), SCHED_OTHER, &param);

        // Set nice value for better scheduling
        if (nice(-15) == -1) {
            // Silently ignore if we can't set nice value
        }
    }
}
#endif

ALSAMIDIInput::ALSAMIDIInput()
    : initialized_(false)
    , input_active_(false)
    , should_stop_(false)
#ifdef __linux__
    , seq_handle_(nullptr)
    , client_id_(-1)
    , port_id_(-1)
    , queue_id_(-1)
#endif
{
}

ALSAMIDIInput::~ALSAMIDIInput() {
    Cleanup();
}

bool ALSAMIDIInput::Initialize() {
#ifdef __linux__
    if (initialized_) {
        return true;
    }
    
    // Open ALSA sequencer in non-blocking mode
    int result = snd_seq_open(&seq_handle_, "default", SND_SEQ_OPEN_DUPLEX, SND_SEQ_NONBLOCK);
    if (result < 0) {
        std::cerr << "Failed to open ALSA sequencer: " << snd_strerror(result) << std::endl;
        return false;
    }
    
    // Set client name
    snd_seq_set_client_name(seq_handle_, "PianoWo MIDI Input");
    
    // Get our client ID
    client_id_ = snd_seq_client_id(seq_handle_);
    if (client_id_ < 0) {
        std::cerr << "Failed to get ALSA client ID: " << snd_strerror(client_id_) << std::endl;
        snd_seq_close(seq_handle_);
        seq_handle_ = nullptr;
        return false;
    }
    
    // Create input port
    port_id_ = snd_seq_create_simple_port(seq_handle_, "MIDI Input",
                                         SND_SEQ_PORT_CAP_WRITE | SND_SEQ_PORT_CAP_SUBS_WRITE,
                                         SND_SEQ_PORT_TYPE_MIDI_GENERIC | SND_SEQ_PORT_TYPE_APPLICATION);
    if (port_id_ < 0) {
        std::cerr << "Failed to create ALSA port: " << snd_strerror(port_id_) << std::endl;
        snd_seq_close(seq_handle_);
        seq_handle_ = nullptr;
        return false;
    }
    
    // Create queue
    queue_id_ = snd_seq_alloc_named_queue(seq_handle_, "PianoWo Queue");
    if (queue_id_ < 0) {
        std::cerr << "Failed to create ALSA queue: " << snd_strerror(queue_id_) << std::endl;
        snd_seq_close(seq_handle_);
        seq_handle_ = nullptr;
        return false;
    }

    // Start the queue
    snd_seq_start_queue(seq_handle_, queue_id_, nullptr);
    snd_seq_drain_output(seq_handle_);

    // Set client pool size to handle more events
    snd_seq_client_pool_t* pool;
    snd_seq_client_pool_alloca(&pool);
    snd_seq_get_client_pool(seq_handle_, pool);
    snd_seq_client_pool_set_input_pool(pool, 1024);
    snd_seq_client_pool_set_output_pool(pool, 1024);
    snd_seq_set_client_pool(seq_handle_, pool);
    
    initialized_ = true;
    std::cout << "ALSA MIDI initialized successfully (Client ID: " << client_id_ << ", Port ID: " << port_id_ << ")" << std::endl;
    return true;
#else
    std::cout << "ALSA MIDI not available on this platform" << std::endl;
    return false;
#endif
}

void ALSAMIDIInput::Cleanup() {
#ifdef __linux__
    StopInput();
    CloseDevice();
    
    if (seq_handle_) {
        if (queue_id_ >= 0) {
            snd_seq_free_queue(seq_handle_, queue_id_);
            queue_id_ = -1;
        }
        snd_seq_close(seq_handle_);
        seq_handle_ = nullptr;
    }
    
    client_id_ = -1;
    port_id_ = -1;
    initialized_ = false;
    std::cout << "ALSA MIDI cleaned up" << std::endl;
#endif
}

std::vector<ALSAMIDIDevice> ALSAMIDIInput::GetInputDevices() const {
    std::vector<ALSAMIDIDevice> devices;
    
#ifdef __linux__
    if (!initialized_ || !seq_handle_) {
        return devices;
    }
    
    snd_seq_client_info_t* client_info;
    snd_seq_port_info_t* port_info;
    
    snd_seq_client_info_alloca(&client_info);
    snd_seq_port_info_alloca(&port_info);
    
    snd_seq_client_info_set_client(client_info, -1);
    
    while (snd_seq_query_next_client(seq_handle_, client_info) >= 0) {
        int client_id = snd_seq_client_info_get_client(client_info);
        
        // Skip our own client and system clients
        if (client_id == client_id_ || client_id == 0) {
            continue;
        }
        
        snd_seq_port_info_set_client(port_info, client_id);
        snd_seq_port_info_set_port(port_info, -1);
        
        while (snd_seq_query_next_port(seq_handle_, port_info) >= 0) {
            unsigned int capability = snd_seq_port_info_get_capability(port_info);
            
            // Check if this port can read (output MIDI data)
            if (capability & SND_SEQ_PORT_CAP_READ) {
                ALSAMIDIDevice device;
                device.client_id = client_id;
                device.port_id = snd_seq_port_info_get_port(port_info);
                device.name = snd_seq_port_info_get_name(port_info);
                device.full_name = GetClientName(client_id) + ":" + device.name;
                device.is_input = true;
                device.is_output = capability & SND_SEQ_PORT_CAP_WRITE;
                
                devices.push_back(device);
            }
        }
    }
#endif
    
    return devices;
}

bool ALSAMIDIInput::OpenDevice(int client_id, int port_id) {
#ifdef __linux__
    if (!initialized_ || !seq_handle_) {
        std::cerr << "ALSA MIDI not initialized" << std::endl;
        return false;
    }
    
    CloseDevice();
    
    // Connect to the specified device
    snd_seq_addr_t sender;
    sender.client = client_id;
    sender.port = port_id;
    
    snd_seq_addr_t dest;
    dest.client = client_id_;
    dest.port = port_id_;
    
    int result = snd_seq_connect_from(seq_handle_, port_id_, sender.client, sender.port);
    if (result < 0) {
        std::cerr << "Failed to connect to MIDI device " << client_id << ":" << port_id 
                  << " - " << snd_strerror(result) << std::endl;
        return false;
    }
    
    // Store current device info
    current_device_.client_id = client_id;
    current_device_.port_id = port_id;
    current_device_.name = GetPortName(client_id, port_id);
    current_device_.full_name = GetClientName(client_id) + ":" + current_device_.name;
    current_device_.is_input = true;
    current_device_.is_output = false;
    
    std::cout << "Connected to ALSA MIDI device: " << current_device_.full_name << std::endl;
    return true;
#else
    return false;
#endif
}

void ALSAMIDIInput::CloseDevice() {
#ifdef __linux__
    if (initialized_ && seq_handle_ && current_device_.client_id >= 0) {
        snd_seq_disconnect_from(seq_handle_, port_id_, current_device_.client_id, current_device_.port_id);
        std::cout << "Disconnected from ALSA MIDI device: " << current_device_.full_name << std::endl;
    }
    
    current_device_ = ALSAMIDIDevice{};
    current_device_.client_id = -1;
    current_device_.port_id = -1;
#endif
}

bool ALSAMIDIInput::IsDeviceOpen() const {
#ifdef __linux__
    return initialized_ && current_device_.client_id >= 0;
#else
    return false;
#endif
}

ALSAMIDIDevice ALSAMIDIInput::GetCurrentDevice() const {
    return current_device_;
}

void ALSAMIDIInput::SetMIDICallback(ALSAMIDIInputCallback callback) {
    midi_callback_ = callback;
}

bool ALSAMIDIInput::StartInput() {
#ifdef __linux__
    if (!initialized_ || !seq_handle_ || input_active_.load()) {
        return false;
    }
    
    should_stop_.store(false);
    input_active_.store(true);
    
    input_thread_ = std::make_unique<std::thread>(&ALSAMIDIInput::InputThreadFunction, this);
    
    std::cout << "ALSA MIDI input started" << std::endl;
    return true;
#else
    return false;
#endif
}

void ALSAMIDIInput::StopInput() {
    if (input_active_.load()) {
        should_stop_.store(true);
        
        if (input_thread_ && input_thread_->joinable()) {
            input_thread_->join();
        }
        
        input_thread_.reset();
        input_active_.store(false);
        
        std::cout << "ALSA MIDI input stopped" << std::endl;
    }
}

bool ALSAMIDIInput::IsInputActive() const {
    return input_active_.load();
}

#ifdef __linux__
void ALSAMIDIInput::InputThreadFunction() {
    // Set real-time priority for ALSA MIDI input thread
    SetALSARealtimePriority();

    // Use poll to wait for events efficiently
    struct pollfd pfd;
    int npfd = snd_seq_poll_descriptors_count(seq_handle_, POLLIN);
    if (npfd <= 0) {
        std::cerr << "Failed to get poll descriptors count" << std::endl;
        return;
    }

    // Pre-allocate event buffer for batch processing
    constexpr int MAX_BATCH_SIZE = 32;
    snd_seq_event_t* events[MAX_BATCH_SIZE];
    MIDIMessage messages[MAX_BATCH_SIZE];

    // Cache timestamp calculation base
    auto start_time = std::chrono::high_resolution_clock::now();
    auto start_time_double = std::chrono::duration<double>(start_time.time_since_epoch()).count();

    while (!should_stop_.load()) {
        // Check for available events with a timeout
        if (snd_seq_poll_descriptors(seq_handle_, &pfd, npfd, POLLIN) < 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            continue;
        }

        int poll_result = poll(&pfd, npfd, 1); // 1ms timeout for lower latency
        if (poll_result < 0) {
            if (errno == EINTR) continue; // Interrupted, try again
            std::cerr << "Poll error: " << strerror(errno) << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            continue;
        }

        if (poll_result == 0) {
            // Timeout, continue
            continue;
        }

        // Process events in batches for better performance
        int batch_count = 0;
        while (!should_stop_.load() && batch_count < MAX_BATCH_SIZE) {
            int result = snd_seq_event_input(seq_handle_, &events[batch_count]);

            if (result < 0) {
                if (result == -EAGAIN) {
                    // No more events available
                    break;
                } else if (result == -ENOSPC) {
                    // No space, try to drop input and continue
                    snd_seq_drop_input(seq_handle_);
                    break;
                } else {
                    std::cerr << "Error reading MIDI event: " << snd_strerror(result) << std::endl;
                    break;
                }
            }

            if (events[batch_count]) {
                // Convert to MIDI message immediately (avoid extra function call overhead)
                messages[batch_count] = ConvertToMIDIMessageOptimized(events[batch_count], start_time_double);
                batch_count++;
            }
        }

        // Process batch of events
        if (batch_count > 0 && midi_callback_) {
            for (int i = 0; i < batch_count; ++i) {
                if (messages[i].status != 0) {
                    midi_callback_(messages[i]);
                }
                snd_seq_free_event(events[i]);
            }
        }
    }
}

void ALSAMIDIInput::ProcessMIDIEvent(const snd_seq_event_t* event) {
    if (!midi_callback_) {
        return;
    }
    
    MIDIMessage message = ConvertToMIDIMessage(event);
    
    // Only process note events for now
    if (message.status != 0) {
        midi_callback_(message);
    }
}

MIDIMessage ALSAMIDIInput::ConvertToMIDIMessage(const snd_seq_event_t* event) {
    MIDIMessage message = {};

    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    message.timestamp = std::chrono::duration<double>(duration).count();

    switch (event->type) {
        case SND_SEQ_EVENT_NOTEON:
            message.status = 0x90 | (event->data.note.channel & 0x0F);
            message.data1 = event->data.note.note;
            message.data2 = event->data.note.velocity;
            break;

        case SND_SEQ_EVENT_NOTEOFF:
            message.status = 0x80 | (event->data.note.channel & 0x0F);
            message.data1 = event->data.note.note;
            message.data2 = event->data.note.velocity;
            break;

        case SND_SEQ_EVENT_CONTROLLER:
            message.status = 0xB0 | (event->data.control.channel & 0x0F);
            message.data1 = event->data.control.param;
            message.data2 = event->data.control.value;
            break;

        case SND_SEQ_EVENT_PGMCHANGE:
            message.status = 0xC0 | (event->data.control.channel & 0x0F);
            message.data1 = event->data.control.value;
            message.data2 = 0;
            break;

        default:
            // Unsupported event type
            message.status = 0;
            break;
    }

    return message;
}

// Optimized version for batch processing - avoids repeated timestamp calculations
MIDIMessage ALSAMIDIInput::ConvertToMIDIMessageOptimized(const snd_seq_event_t* event, double base_timestamp) {
    MIDIMessage message = {};

    // Use a simple offset from base timestamp for better performance
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    message.timestamp = std::chrono::duration<double>(duration).count();

    switch (event->type) {
        case SND_SEQ_EVENT_NOTEON:
            message.status = 0x90 | (event->data.note.channel & 0x0F);
            message.data1 = event->data.note.note;
            message.data2 = event->data.note.velocity;
            break;

        case SND_SEQ_EVENT_NOTEOFF:
            message.status = 0x80 | (event->data.note.channel & 0x0F);
            message.data1 = event->data.note.note;
            message.data2 = event->data.note.velocity;
            break;

        case SND_SEQ_EVENT_CONTROLLER:
            message.status = 0xB0 | (event->data.control.channel & 0x0F);
            message.data1 = event->data.control.param;
            message.data2 = event->data.control.value;
            break;

        case SND_SEQ_EVENT_PGMCHANGE:
            message.status = 0xC0 | (event->data.control.channel & 0x0F);
            message.data1 = event->data.control.value;
            message.data2 = 0;
            break;

        default:
            // Unsupported event type
            message.status = 0;
            break;
    }

    return message;
}

std::string ALSAMIDIInput::GetClientName(int client_id) const {
    snd_seq_client_info_t* client_info;
    snd_seq_client_info_alloca(&client_info);
    
    if (snd_seq_get_any_client_info(seq_handle_, client_id, client_info) >= 0) {
        return std::string(snd_seq_client_info_get_name(client_info));
    }
    
    return "Unknown Client";
}

std::string ALSAMIDIInput::GetPortName(int client_id, int port_id) const {
    snd_seq_port_info_t* port_info;
    snd_seq_port_info_alloca(&port_info);
    
    if (snd_seq_get_any_port_info(seq_handle_, client_id, port_id, port_info) >= 0) {
        return std::string(snd_seq_port_info_get_name(port_info));
    }
    
    return "Unknown Port";
}
#endif
